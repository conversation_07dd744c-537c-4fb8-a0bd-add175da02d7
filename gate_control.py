import time
import cv2
import requests
import json
import RPi.GPIO as GPIO
from pyzbar.pyzbar import decode
from config import Config as config_env
from camera_processor import CameraDevice


camera = CameraDevice(
    device=0,
    width=1920,
    height=1080,
    fps=30,
    stream_type="mjpeg"
)

cap = camera.setup()

# --- Ultrasonic Sensor Pins ---
TRIG = config_env.TRIG
ECHO = config_env.ECHO

# --- Relay GPIO Setup ---
RELAY1_PIN = config_env.RELAY1_PIN
RELAY2_PIN = config_env.RELAY2_PIN
RELAY3_PIN = config_env.RELAY3_PIN


GPIO.setmode(GPIO.BCM)

# Setup pins
GPIO.setup(TRIG, GPIO.OUT)
GPIO.setup(ECHO, GPIO.IN)
GPIO.setup(RELAY1_PIN, GPIO.OUT, initial=GPIO.HIGH)  # HIGH = OFF
GPIO.setup(RELAY2_PIN, GPIO.OUT, initial=GPIO.HIGH)
GPIO.setup(RELAY3_PIN, GPIO.OUT, initial=GPIO.HIGH)

# --- Distance Measurement Function ---
def read_distance():
    """Measure distance using HC-SR04 ultrasonic sensor (in cm)."""
    # Send 10us pulse
    GPIO.output(TRIG, True)
    time.sleep(0.00001)
    GPIO.output(TRIG, False)

    # Wait for echo start
    while GPIO.input(ECHO) == 0:
        pulse_start = time.time()

    # Wait for echo end
    while GPIO.input(ECHO) == 1:
        pulse_end = time.time()

    pulse_duration = pulse_end - pulse_start
    distance = pulse_duration * 17150  # convert to cm
    return round(distance, 2)

# --- Relay Functions ---
def activate_relay1():
    """Pulse Relay1 (Gate Open)."""
    print("Gate Open (Relay1 ON)")
    GPIO.output(RELAY1_PIN, GPIO.LOW)   # Relay1 ON
    time.sleep(2)  # keep ON for 2 sec (adjust as needed)
    GPIO.output(RELAY1_PIN, GPIO.HIGH)  # Relay1 OFF
    print("Relay1 OFF")

def scan_qr(timeout=5):
    """Scan QR codes with camera for 'timeout' seconds."""
    
    start_time = time.time()
    qr_code = None

    print("Opening camera and scanning QR...")

    while time.time() - start_time < timeout and camera.is_opened():
        frame = camera.read_frames()
        codes = decode(frame)
        if codes:
            qr_code = codes[0].data.decode("utf-8")
            break
        
    return qr_code

def mode_check():
    """Mode check loop: keep Relay2 ON while distance in threshold and read QR.
       After Relay2 OFF -> wait 0.5 sec -> Relay3 ON (1 sec) -> Relay3 OFF -> back to normal.
    """
    print("Entering Mode Check...")
    GPIO.output(RELAY2_PIN, GPIO.LOW)  # Relay2 ON


    try:
        while True:
            # --- Distance Check ---
            distance = read_distance()
            print(f"[ModeCheck] Distance: {distance} cm")

            if 30 < distance < 700:
                GPIO.output(RELAY2_PIN, GPIO.LOW)  # keep relay ON
            else:
                print("No object detected in range, exiting Mode Check...")
                GPIO.output(RELAY2_PIN, GPIO.HIGH)  # Relay2 OFF
                time.sleep(0.00001)  # wait 0.5 sec

                # Relay3 pulse
                GPIO.output(RELAY1_PIN, GPIO.LOW)  # Relay3 ON
                print("Relay11 ON")
                time.sleep(0.2)  # keep ON for 1 sec
                GPIO.output(RELAY1_PIN, GPIO.HIGH)  # Relay3 OFF
                print("Relay3 OFF")

                break  # exit mode_check, go back to normal mode

            # --- QR Code Check ---
            frame = camera.read_frames()

            if frame is not None:
                codes = decode(frame)
                for code in codes:
                    qr_data = code.data.decode("utf-8")
                    print(f"[{qr_data}] QR Code Found in Mode Check")

            time.sleep(0.5)

    except KeyboardInterrupt:
        GPIO.output(RELAY2_PIN, GPIO.HIGH)
        raise

def check_cart_status(qr_data):
    """Check the status of the cart (e.g., weight, contents)."""
    print("Checking cart status...")
    # Simulate checking cart status
    data = {"store_id": qr_data["store_id"],
            "cart_id": qr_data["cart_id"],
            "requested_by": "hardware"}
    response = requests.post("https://sk-gcp-replica-1.vishwamcorp.com/memory-service/v1/get_active_carts", data=json.dumps(data))
    print(f"Response: {response.json()}")
    print("Cart status checked.")

# --- Main Loop ---
try:
    while True:
        distance = read_distance()
        print(f"[Normal] Distance: {distance} cm")
        if 25 < distance < 70:
            print(f"[Normal] Distance within range: {distance} cm")
            try:
                qr_result = scan_qr(timeout=3)
            except Exception as e:
                print(f"Error scanning QR code: {e}")
                qr_result = None
                continue
            if qr_result:
                print(f"QR Code Found: {qr_result}")
                check_cart_status(qr_result)
                
                if qr_result == "cart no 1 | 371K1Y7U":
                    activate_relay1()
                    mode_check()  # switch to mode check
            else:
                print("No QR code detected.")

        time.sleep(1)

except KeyboardInterrupt:
    print("Exiting...")

finally:
    GPIO.cleanup()
    camera.close()
