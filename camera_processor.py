from dataclasses import dataclass
from typing import Literal

import cv2
import asyncio
import os
import queue
import subprocess
import sys
import threading
import time
import numpy as np
from turbojpeg import TurboJPEG




@dataclass
class CameraDevice:
    """
    Camera device class

    It holds single camera parameters and setup the camera device

    Kwargs:
        device (Optional[Union[int, str]]): Camera device to use (can be char device path, int device ID or None)
        cam_type (Literal["usb", "csi"]): Camera type to use for this camera
        stream_type (Literal["raw", "mjpeg"]): Stream type to use for this camera
        width (int): Width of the frame to capture
        height (int): Height of the frame to capture
        fps (int): FPS of the frame to capture
        fourcc (str): FourCC of the frame to capture
        force_cap_en (bool): Capturing is started for camera in `Force capture mode`
        exposure_mode (Literal["auto", "manual"]): Exposure mode to use for this camera
        exposure_value (int): Exposure value to use for this camera
        cli_commands (List[str]): List of CLI commands to execute while setup
        event_exit (threading.Event): Event to exit and close instance
        thread_sync (ThreadSync): ThreadSync instance to synchronize threads
        frames_queue (asyncio.Queue): Queue to put frames to (not used if set to None)
    """

    cam_type: Literal["usb", "csi"] = "usb"
    stream_type: Literal["raw", "mjpeg"] = "raw"
    width: int = 1920
    height: int = 1080
    fps: int = 15  # We want this FPS to capture
    fourcc: str = "MJPG"
    force_cap_en: bool = False
    exposure_mode: Literal["auto", "manual"] = "auto"
    exposure_value: int = 100

    def __post_init__(self):
        if self.width == 0:
            raise ValueError("Width cannot be 0")

        if self.height == 0:
            raise ValueError("Height cannot be 0")

        if self.event_exit is None:
            raise ValueError("Exit event is not specified")

        if self.thread_sync is None:
            raise ValueError("No thread sync specified")

        ##
        # Device capture object
        self.cap = None

        ##
        # Real FPS of the camera (got from CV2)
        self._real_fps = self.fps

        ##
        # Flag to enable/disable capturing
        self._enable_capture = False

        ##
        # Thread to capture frames from the camera
        self.threadCapture = None

        ##
        # Thread to decode frames from camera
        self.threadDecode = None

        ##
        # Measured FPS of the camera
        self.measured_fps = 0.0
        
        ##
        # Decoder queue.
        self.decode_queue = queue.Queue(30)

        ##
        # TurboJPEG decoder
        self.turbo_jpeg = TurboJPEG()

        ##
        # Camera device index
        self.device = 0
        
        


    def __del__(self):
        self.close()

    def close(self) -> None:
        """
        Close the camera device
        """
        try:
            if self.cap is not None:
                self.cap.release()
                self.cap = None
        except Exception as e:
            pass

    def setup(self) -> cv2.VideoCapture:
        """
        Setup the camera device

        Setup the camera device for needed hardware parameters

        Returns:
            cv2.VideoCapture: Opened camera device
        """
        if self.cap is None:
            self.cap = cv2.VideoCapture(self.device, cv2.CAP_V4L2)

            if not self.cap.isOpened():
                raise Exception(f"CameraDevice: Failed to open {self.stream_type} camera device '{self.device}'")

            if self.stream_type == "mjpeg":
                # Remove MJPEG->RAW conversion
                self.cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)

            if self.fourcc:
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*self.fourcc))

            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)  # 0 = off, 1 = on for auto focus

            if self.fps != 0:
                self.cap.set(cv2.CAP_PROP_FPS, self.fps)

            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            self._real_fps = self.cap.get(cv2.CAP_PROP_FPS)
            fourcc_int = int(self.cap.get(cv2.CAP_PROP_FOURCC))

            print(
                "Source Video: %u x %u, fps = %u, fourcc = 0x%08X" % (self.width, self.height, self.fps, fourcc_int)
            )

            try:
                if self.exposure_mode == "manual":
                    self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 1)
                    self.cap.set(cv2.CAP_PROP_EXPOSURE, self.exposure_value)
                elif self.exposure_mode == "auto":
                    self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 3)

                self.__cli_setup()
            except Exception as e:
                print(f"CameraDevice: Failed to set up {self.stream_type} camera device '{self.device}': {e}")

        return self.cap
    
#   self.cap = self.camera.setup()
        

        
        
    def read_frames(self):
        """
        Read frames from the camera
        """
        if self.cap is None:
            raise ValueError("Camera is not opened")

        ret, frame = self.cap.read()
        if not ret:
            raise ValueError("Failed to read frame from camera")

        return frame

