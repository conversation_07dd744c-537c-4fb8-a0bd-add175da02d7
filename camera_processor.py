"""
@file camera_processor.py
@brief Camera processor class to capture frames from a camera,
       Process them and send to the output queue
@Author: <PERSON><PERSON><PERSON> (<EMAIL>)
"""


# Import necessary libraries
from dataclasses import dataclass, field
from typing import Literal, Optional, Union, List
import cv2
import asyncio
import queue
import subprocess
import threading
import numpy as np

try:
    from turbojpeg import TurboJPEG
except ImportError:
    TurboJPEG = None


@dataclass
class CameraDevice:
    """
    Camera device class

    It holds single camera parameters and setup the camera device

    Kwargs:
        device (Optional[Union[int, str]]): Camera device to use (can be char device path, int device ID or None)
        cam_type (Literal["usb", "csi"]): Camera type to use for this camera
        stream_type (Literal["raw", "mjpeg"]): Stream type to use for this camera
        width (int): Width of the frame to capture
        height (int): Height of the frame to capture
        fps (int): FPS of the frame to capture
        fourcc (str): FourCC of the frame to capture
        force_cap_en (bool): Capturing is started for camera in `Force capture mode`
        exposure_mode (Literal["auto", "manual"]): Exposure mode to use for this camera
        exposure_value (int): Exposure value to use for this camera
        cli_commands (List[str]): List of CLI commands to execute while setup
        event_exit (threading.Event): Event to exit and close instance
        thread_sync: ThreadSync instance to synchronize threads
        frames_queue (asyncio.Queue): Queue to put frames to (not used if set to None)
    """

    device: Optional[Union[int, str]] = 0
    cam_type: Literal["usb", "csi"] = "usb"
    stream_type: Literal["raw", "mjpeg"] = "raw"
    width: int = 1920
    height: int = 1080
    fps: int = 15  # We want this FPS to capture
    fourcc: str = "MJPG"
    force_cap_en: bool = False
    exposure_mode: Literal["auto", "manual"] = "auto"
    exposure_value: int = 100
    cli_commands: List[str] = field(default_factory=list)
    event_exit: Optional[threading.Event] = None
    thread_sync: Optional[object] = None
    frames_queue: Optional[asyncio.Queue] = None

    def __post_init__(self):
        if self.width == 0:
            raise ValueError("Width cannot be 0")

        if self.height == 0:
            raise ValueError("Height cannot be 0")

        ##
        # Device capture object
        self.cap = None

        ##
        # Real FPS of the camera (got from CV2)
        self._real_fps = self.fps

        ##
        # Flag to enable/disable capturing
        self._enable_capture = False

        ##
        # Thread to capture frames from the camera
        self.threadCapture = None

        ##
        # Thread to decode frames from camera
        self.threadDecode = None

        ##
        # Measured FPS of the camera
        self.measured_fps = 0.0

        ##
        # Decoder queue.
        self.decode_queue = queue.Queue(30)

        ##
        # TurboJPEG decoder (only initialize if needed for MJPEG)
        self.turbo_jpeg = None
        if self.stream_type == "mjpeg" and TurboJPEG is not None:
            try:
                self.turbo_jpeg = TurboJPEG()
            except Exception as e:
                print(f"Warning: TurboJPEG initialization failed: {e}, falling back to OpenCV for MJPEG decoding")

        ##
        # Initialize camera setup flag
        self._is_setup = False
        
        


    def __del__(self):
        self.close()

    def close(self) -> None:
        """
        Close the camera device
        """
        try:
            if self.cap is not None:
                self.cap.release()
                self.cap = None
        except Exception:
            pass

    def setup(self) -> cv2.VideoCapture:
        """
        Setup the camera device

        Setup the camera device for needed hardware parameters

        Returns:
            cv2.VideoCapture: Opened camera device
        """
        if self._is_setup and self.cap is not None:
            return self.cap

        if self.cap is None:
            # Try different backends based on camera type
            if self.cam_type == "csi":
                # For CSI cameras, try GStreamer first, then V4L2
                try:
                    self.cap = cv2.VideoCapture(self.device, cv2.CAP_GSTREAMER)
                except:
                    self.cap = cv2.VideoCapture(self.device, cv2.CAP_V4L2)
            else:
                # For USB cameras, use V4L2 on Linux
                self.cap = cv2.VideoCapture(self.device, cv2.CAP_V4L2)

            if not self.cap.isOpened():
                # Fallback to default backend
                self.cap = cv2.VideoCapture(self.device)
                if not self.cap.isOpened():
                    raise Exception(f"CameraDevice: Failed to open {self.stream_type} camera device '{self.device}'")

            # Configure stream type
            if self.stream_type == "mjpeg":
                # Remove MJPEG->RAW conversion for better performance
                self.cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)

            # Set codec if specified
            if self.fourcc:
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*self.fourcc))

            # Set resolution
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)

            # Disable autofocus for consistent performance
            self.cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)

            # Set FPS
            if self.fps > 0:
                self.cap.set(cv2.CAP_PROP_FPS, self.fps)

            # Get actual values from camera
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self._real_fps = self.cap.get(cv2.CAP_PROP_FPS)
            fourcc_int = int(self.cap.get(cv2.CAP_PROP_FOURCC))

            print(f"Camera initialized: {self.width}x{self.height}, fps={self._real_fps}, fourcc=0x{fourcc_int:08X}")

            # Configure exposure
            try:
                if self.exposure_mode == "manual":
                    self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 1)  # Manual exposure
                    self.cap.set(cv2.CAP_PROP_EXPOSURE, self.exposure_value)
                elif self.exposure_mode == "auto":
                    self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 3)  # Auto exposure
            except Exception as e:
                print(f"Warning: Failed to set exposure mode: {e}")

            # Execute CLI commands if any
            try:
                self._execute_cli_commands()
            except Exception as e:
                print(f"Warning: Failed to execute CLI commands: {e}")

            self._is_setup = True

        return self.cap

    def _execute_cli_commands(self) -> None:
        """
        Execute CLI commands for camera setup
        """
        if not self.cli_commands:
            return

        for command in self.cli_commands:
            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True)
                if result.returncode != 0:
                    print(f"Warning: CLI command failed: {command}")
                    print(f"Error: {result.stderr}")
                else:
                    print(f"CLI command executed successfully: {command}")
            except Exception as e:
                print(f"Warning: Failed to execute CLI command '{command}': {e}")

    def read_frames(self) -> np.ndarray:
        """
        Read frames from the camera

        Returns:
            np.ndarray: The captured frame

        Raises:
            ValueError: If camera is not setup or frame reading fails
        """
        if not self._is_setup or self.cap is None:
            raise ValueError("Camera is not setup. Call setup() first.")

        ret, frame = self.cap.read()
        if not ret:
            raise ValueError("Failed to read frame from camera")

        # Handle MJPEG decoding if using TurboJPEG
        if self.stream_type == "mjpeg" and self.turbo_jpeg is not None:
            try:
                # Decode MJPEG frame using TurboJPEG for better performance
                frame = self.turbo_jpeg.decode(frame)
            except Exception as e:
                print(f"Warning: TurboJPEG decode failed, using OpenCV: {e}")
                # Fall back to OpenCV decoding

        return frame

    def is_opened(self) -> bool:
        """
        Check if camera is opened and ready

        Returns:
            bool: True if camera is opened and setup, False otherwise
        """
        return self._is_setup and self.cap is not None and self.cap.isOpened()

    def get_frame_size(self) -> tuple:
        """
        Get the current frame size

        Returns:
            tuple: (width, height) of the frames
        """
        return (self.width, self.height)

    def get_fps(self) -> float:
        """
        Get the actual FPS of the camera

        Returns:
            float: The real FPS value from the camera
        """
        return self._real_fps


# Create camera device
camera = CameraDevice(
    device=0,
    width=1920,
    height=1080,
    fps=30,
    stream_type="mjpeg"
)

# Setup camera
cap = camera.setup()

# Read frames
while camera.is_opened():
    frame = camera.read_frames()
    # Process frame....


